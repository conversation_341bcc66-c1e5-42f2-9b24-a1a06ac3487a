@import '../vw_values.less';
@import '../constants.less'; 
header {
    position: fixed;
    padding-top: @vw22;
    top: 0;
    width: 100%;
    left: 0;
    z-index: 99;
    > .background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: @vw100 * 3;
      pointer-events: none;
      z-index: -1;
      backdrop-filter: blur(@vw50); // Pas de blur-waarde aan naar wens
      -webkit-backdrop-filter: blur(10px); // Voor Safari-ondersteuning
      background: rgba(0,0,0,0); // Zorg ervoor dat de achtergrond deels transparant is
      -webkit-mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
      mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
    }
    .innerMenu {
        display: inline-block;
        vertical-align: middle;
        margin-left: @vw100 + @vw60;
        list-style: none;
        li {
          display: inline-block;
        }
        a {
            display: inline-block;
            vertical-align: middle;
            padding: @vw10;
            cursor: pointer;
            color: @almostWhite;
            text-decoration: none;
            transition: color .3s, transform .3s;
            &:not(:last-of-type) {
                margin-right: @vw22;
            }
            &:hover {
                color: @primaryColor;
            }
        }
    }
    .col {
        display: inline-block;
        width: 75%;
        vertical-align: middle;
        position: relative;
        &:last-child {
            text-align: right;
            width: 25%;
            padding-right: @vw100 + @vw30;
        } 
        .logo {
            display: inline-block;
            width: @vw100 * 2;
            height: auto;
            vertical-align: middle;
            position: relative;
            opacity: 1;
            .transition(.3s);
            &:hover {
                opacity: .4;
            }
            svg {
              height: auto;
              width: 100%;
              object-fit: contain;
            }
        }
        .socials {
            display: inline-block;
            vertical-align: middle;
            position: relative;
            z-index: 10;
            &.active {
              .social {
                color: @almostBlack;
                &:hover {
                  color: @primaryColor;
                }
              }
            }
            .social {
                display: inline-block;
                padding: @vw10;
                cursor: pointer;
                color: @almostWhite;
                text-decoration: none;
                vertical-align: middle;
                .transition(.3s);
                &:hover {
                    color: @primaryColor;
                }
            }
        }
        .button {
            display: inline-block;
            margin-left: @vw40;
            vertical-align: middle;
        }
    }
}

#menu {
  display: inline-block;
  padding: @vw20;
  cursor: pointer;
  position: absolute;
  right: 0; 
  top: -@vw12;
  color: @almostWhite;
  font-size: @vw21;
  z-index: 9;
  .transition(.3s);
  .background {
    height: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    background: @grey;
    .transitionMore(opacity, .3s, 0s, ease-in);
  }
  &.active {
    color: @almostBlack;
    .background {
      opacity: 1;

    }
    .hamburger {
      .border {
        background: @almostBlack;
        &:nth-child(1) {
          .transform(translateY(@vw6) rotate(-45deg));
        }
        &:nth-child(2) {
          width: 0%;
        }
        &:nth-child(3) {
          .transform(translateY(-@vw6) rotate(45deg));
        }
      }
    }
    .innerContent {
      display: block;
      width: (@vw99 * 4) + (@vw22 * 3);
    }
  }
  div, span {
    cursor: pointer;
    display: inline-block;
  }
  .innerContent {
    display: none;
    padding: @vw40 0;
    &.showContent {
      ul {
        &.hover {
          li {
            a {
              opacity: .2;
            }
            &.active {
              a {
                opacity: 1;
              }
            }
          }
        }
        li {
          visibility: visible;
          opacity: 1;
          transform: translateY(0);
          a {
            
          }
          &:nth-child(2) {
            transition-delay: .15s;
          }
          &:nth-child(3) {
            transition-delay: .3s;
          }
          &:nth-child(4) {
            transition-delay: .45s;
          }
          &:nth-child(5) {
            transition-delay: .6s;
          }
          &:nth-child(6) {
            transition-delay: .75s;
          }
        }
      }
    }
    > div {
      display: block;
    }
    .menu-primary-menu-container {
      padding-bottom: @vw20;
      ul {
        li {
          font-size: @vw24;
          line-height: 1.4;
          font-family: "owners-xwide", sans-serif;
          font-weight: 500;
        }
      }
    }
    ul {
      list-style: none;
      li {
        font-family: "owners", sans-serif;
        font-size: @vw21;
        font-weight: 600;
        padding-bottom: @vw10;
        line-height: @vw31;
        text-transform: uppercase;
        visibility: hidden;
        position: relative;
        opacity: 0;
        transform: translateY(@vw20);
        .transition(.3s);
        a {
          cursor: pointer;
          color: @almostBlack;
          text-decoration: none;
          .transition(.15s);
          padding-right: @vw50;
            &:after {
              font-family: "icomoon";
              content: '\e900';
              position: absolute;
              right: 0;
              top: -@vw2;
              .transitionMore(transform, .3s);
            }
            &:hover {
              &:after {
                transform: scale(.8);
              }
            }
        }
      }
    }
  }
  .hamburger {
    cursor: pointer;
    margin-left: @vw14;
    display: inline-block;
    height: @vw14;
    width: @vw16;
    .border {
      position: absolute;
      display: block;
      height: 2px;
      width: 100%;
      border-radius: @vw2;
      background: @almostWhite;
      .transition(.3s);
      &:first-child {
        top: 0;
      }
      &:nth-child(2) {
        top: 50%;
        transform: translateY(-50%);
      }
      &:nth-child(3) {
        bottom: 0;
        top: auto;
      }
    }
  }
}

  @media all and (max-width: 1080px) {
    header {
      > .background {
        height: @vw100-1080 * 3;
        backdrop-filter: blur(@vw50-1080);
      }
      .col {
        width: 66.6666%;
        &:last-child {
          width: 33.3333%;
        }
      }
      .innerMenu {
          margin-left: @vw100-1080 + @vw60-1080;
          a {
              padding: @vw10-1080;
              &:not(:last-of-type) {
                  margin-right: @vw22-1080;
              }
          }
      }
      .col {
          &:last-child {
              padding-right: @vw100-1080 + @vw30-1080;
          } 
          .logo {
              width: @vw112-1080;
              height: @vw100-1080 + @vw70-1080;
              top: -@vw22-1080;
              svg {
                bottom: @vw25-1080;
              }
          }
          .socials {
              .social {
                  padding: @vw10-1080;
              }
          }
          .button {
              margin-left: @vw40-1080;
          }
      }
  }
  
  #menu {
    padding: @vw20-1080;
    top: -@vw12-1080;
    font-size: @vw21-1080;
    &.active {
      .hamburger {
        .border {
          &:nth-child(1) {
            .transform(translateY(@vw6-1080) rotate(-45deg));
          }
          &:nth-child(3) {
            .transform(translateY(-@vw6-1080) rotate(45deg));
          }
        }
      }
      .innerContent {
        width: (@vw99-1080 * 4) + (@vw22-1080 * 3);
      }
    }
    .innerContent {
      padding: @vw40-1080 0;
      .menu-primary-menu-container {
        padding-bottom: @vw20-1080;
        ul {
          li {
            font-size: @vw22-1080;
            a {
              padding-right: @vw50-1080;
              &:after {
                top: -@vw2-1080;
              }
            }
          }
        }
      }
      ul {
        li {
          font-size: @vw21-1080;
          padding-bottom: @vw10-1080;
          line-height: @vw31-1080;
          transform: translateY(@vw20-1080);
        }
      }
    }
    .hamburger {
      margin-left: @vw14-1080;
      height: @vw14-1080;
      width: @vw16-1080;
      .border {
        border-radius: @vw2-1080;
      }
    }
  }
}

@media all and (max-width: 580px) {
  header {
    padding-top: @vw24-580;
      > .background {
        height: @vw100-580 * 3;
        backdrop-filter: blur(@vw50-580);
      }
      .innerMenu {
          display: none;
      }
      .col {
        width: 25%;
          &:last-child {
            width: 75%;
            padding-right: @vw100-580 + @vw40-580;
          } 
          .logo {
              width: @vw70-580;
              height: @vw90-580;
              top: -@vw50-580;
              svg {
                bottom: @vw25-580;
              }
          }
          .socials {
              .social {
                  padding: @vw10-580;
                  &:not(:last-child) {
                    margin-right: @vw16-580;
                  }
              }
          }
          .button {
              margin-left: @vw40-580;
          }
      }
  }

  #menu {
    padding: @vw20-580;
    top: -@vw12-580;
    font-size: @vw21-580;
    &.active {
      .hamburger {
        .border {
          &:nth-child(1) {
            .transform(translateY(@vw6-580) rotate(-45deg));
          }
          &:nth-child(3) {
            .transform(translateY(-@vw6-580) rotate(45deg));
          }
        }
      }
      .innerContent {
        width: (@vw99-580 * 4) + (@vw22-580 * 3);
      }
    }
    .innerContent {
      padding: @vw40-580 0;
      .menu-primary-menu-container {
        padding-bottom: @vw20-580;
        ul {
          li {
            font-size: @vw26-580;
            a {
              padding-right: @vw50-580;
              &:after {
                top: -@vw2-580;
              }
            }
          }
        }
      }
      ul {
        li {
          font-size: @vw21-580;
          padding-bottom: @vw10-580;
          line-height: @vw50-580;
          transform: translateY(@vw20-580);
        }
      }
    }
    .hamburger {
      margin-left: @vw22-580;
      height: @vw14-580;
      width: @vw22-580;
      .border {
        border-radius: @vw2-580;
      }
    }
  }
}
